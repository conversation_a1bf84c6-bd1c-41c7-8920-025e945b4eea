# MQTT Broker Server (.NET 8)

## 项目概括
本项目旨在开发一个基于 .NET 8 的高性能 MQTT Broker 服务器，作为物联网架构中的核心消息中间件层。该服务器将实现标准 MQTT 协议（支持 MQTT 3.1.1 和 MQTT 5.0），提供可靠的消息路由、高并发连接管理，以及完整的 QoS 级别支持，为物联网设备提供稳定高效的消息通信服务。

## 技术选型
- **主要编程语言**: C# (.NET 8)
- **网络通信框架**: System.Net.Sockets + SocketAsyncEventArgs (高性能异步网络编程)
- **并发处理**: Task Parallel Library (TPL) + Channel<T> (高性能消息队列)
- **内存管理**: System.Buffers (内存池优化)
- **配置管理**: Microsoft.Extensions.Configuration
- **日志记录**: Microsoft.Extensions.Logging + Serilog
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **数据持久化**: SQLite (会话存储) + Redis (可选，集群支持)
- **协议解析**: 自定义 MQTT 协议解析器
- **性能监控**: System.Diagnostics.Metrics
- **测试框架**: xUnit + Moq
- **版本控制**: Git

## 项目结构 / 模块划分
```
/src
  /MqttBroker.Core/              # 核心业务逻辑
    /Protocol/                   # MQTT 协议实现
    /Client/                     # 客户端管理
    /Session/                    # 会话管理
    /Topic/                      # 主题管理
    /Message/                    # 消息处理
    /QoS/                        # QoS 级别处理
  /MqttBroker.Network/           # 网络通信层
    /Server/                     # TCP 服务器
    /Connection/                 # 连接管理
    /PacketHandlers/             # 数据包处理器
  /MqttBroker.Storage/           # 数据存储层
    /Repositories/               # 数据访问
    /Models/                     # 数据模型
  /MqttBroker.Configuration/     # 配置管理
  /MqttBroker.Logging/           # 日志管理
  /MqttBroker.Metrics/           # 性能监控
  /MqttBroker.Host/              # 主机服务
/tests
  /MqttBroker.Tests.Unit/        # 单元测试
  /MqttBroker.Tests.Integration/ # 集成测试
  /MqttBroker.Tests.Performance/ # 性能测试
/docs                            # 文档
/scripts                         # 部署脚本
Program.cs                       # 程序入口点
MqttBroker.sln                   # 解决方案文件
```

## 核心功能 / 模块详解
- **MQTT 协议解析器**: 实现 MQTT 3.1.1 和 5.0 协议的完整解析，包括 CONNECT、PUBLISH、SUBSCRIBE、UNSUBSCRIBE 等所有数据包类型的编码解码。
- **客户端连接管理**: 处理客户端连接建立、身份验证、心跳检测、异常断开重连等连接生命周期管理。
- **主题订阅系统**: 实现高效的主题树结构，支持通配符匹配（+ 和 #），提供快速的主题路由查找。
- **消息路由引擎**: 根据订阅关系将消息准确路由到目标客户端，支持消息过滤和转换。
- **QoS 级别处理**: 完整实现 QoS 0（最多一次）、QoS 1（至少一次）、QoS 2（恰好一次）的消息传递保证。
- **会话持久化**: 管理客户端会话状态，包括订阅信息、未确认消息、遗嘱消息等的持久化存储。
- **遗嘱消息处理**: 实现 Last Will and Testament 机制，在客户端异常断开时自动发布预设的遗嘱消息。
- **高并发网络服务**: 基于异步 I/O 和事件驱动架构，支持数万级并发连接。
- **性能监控系统**: 实时监控连接数、消息吞吐量、内存使用等关键性能指标。

## MQTT 协议数据模型
- **MqttClient**: { ClientId (string), IsConnected (bool), KeepAlive (int), CleanSession (bool), WillMessage (MqttWillMessage), Subscriptions (List<Subscription>), LastActivity (DateTime) }
- **MqttMessage**: { Topic (string), Payload (byte[]), QoSLevel (MqttQoSLevel), Retain (bool), MessageId (ushort), Timestamp (DateTime) }
- **Subscription**: { ClientId (string), TopicFilter (string), QoSLevel (MqttQoSLevel), CreatedAt (DateTime) }
- **MqttSession**: { ClientId (string), IsCleanSession (bool), Subscriptions (List<Subscription>), PendingMessages (Queue<MqttMessage>), LastWillMessage (MqttWillMessage) }

## 技术实现细节

### 项目结构初始化 (已完成)
**实现方案**: 采用标准的 .NET 8 解决方案架构，使用分层设计模式，确保各模块职责清晰、依赖关系合理。

**核心组件设计**:
- **解决方案结构**: 创建了 MqttBroker.sln 解决方案文件，包含 7 个核心项目和 3 个测试项目
- **分层架构**:
  - `MqttBroker.Core`: 核心业务逻辑层，包含协议解析、客户端管理、主题管理等核心功能
  - `MqttBroker.Network`: 网络通信层，负责 TCP 连接管理和数据包处理
  - `MqttBroker.Storage`: 数据存储层，使用 Entity Framework Core + SQLite 进行数据持久化
  - `MqttBroker.Configuration`: 配置管理层，处理应用程序配置
  - `MqttBroker.Logging`: 日志管理层，集成 Serilog 进行结构化日志记录
  - `MqttBroker.Metrics`: 性能监控层，提供实时性能指标收集
  - `MqttBroker.Host`: 主机服务层，作为应用程序入口点

**依赖注入架构**: 每个项目都提供了 `ServiceCollectionExtensions` 类，用于注册该模块的服务到 DI 容器中，确保松耦合和可测试性。

**配置管理**:
- 创建了 `appsettings.json` 和 `appsettings.Development.json` 配置文件
- 支持分环境配置，包括服务器端口、连接限制、存储配置等关键参数

**测试策略**:
- `MqttBroker.Tests.Unit`: 单元测试项目，使用 xUnit + Moq 进行单元测试
- `MqttBroker.Tests.Integration`: 集成测试项目，测试各模块间的协作
- `MqttBroker.Tests.Performance`: 性能测试项目，验证高并发场景下的性能表现

**关键技术选型验证**:
- ✅ .NET 8 目标框架设置完成
- ✅ Microsoft.Extensions.Hosting 集成完成，支持后台服务
- ✅ Entity Framework Core + SQLite 数据存储配置完成
- ✅ Serilog 日志框架集成完成
- ✅ xUnit + Moq 测试框架配置完成
- ✅ 项目间依赖关系正确设置
- ✅ 解决方案构建和测试运行验证通过

## 开发状态跟踪
| 模块/功能                | 状态     | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|--------------------------|----------|--------|--------------|--------------|-----------| 
| 项目结构初始化           | 已完成   | AI     | 2024-12-19   | 2024-12-19   | [详见技术实现细节](#项目结构初始化-已完成) |
| MQTT 协议解析器          | 未开始   | AI     | 2024-12-20   |              |           |
| 网络通信层               | 未开始   | AI     | 2024-12-21   |              |           |
| 客户端连接管理           | 未开始   | AI     | 2024-12-22   |              |           |
| 主题订阅系统             | 未开始   | AI     | 2024-12-23   |              |           |
| 消息路由引擎             | 未开始   | AI     | 2024-12-24   |              |           |
| QoS 级别处理             | 未开始   | AI     | 2024-12-25   |              |           |
| 会话持久化               | 未开始   | AI     | 2024-12-26   |              |           |
| 遗嘱消息处理             | 未开始   | AI     | 2024-12-27   |              |           |
| 性能监控系统             | 未开始   | AI     | 2024-12-28   |              |           |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 开发环境要求
- .NET 8 SDK (已验证)
- Visual Studio 2022 或 VS Code
- Git

### 项目结构
```
MqttBroker/
├── src/
│   ├── MqttBroker.Core/           # 核心业务逻辑
│   ├── MqttBroker.Network/        # 网络通信层
│   ├── MqttBroker.Storage/        # 数据存储层
│   ├── MqttBroker.Configuration/  # 配置管理
│   ├── MqttBroker.Logging/        # 日志管理
│   ├── MqttBroker.Metrics/        # 性能监控
│   └── MqttBroker.Host/           # 主机服务
├── tests/
│   ├── MqttBroker.Tests.Unit/     # 单元测试
│   ├── MqttBroker.Tests.Integration/ # 集成测试
│   └── MqttBroker.Tests.Performance/ # 性能测试
├── docs/                          # 文档
├── scripts/                       # 部署脚本
└── MqttBroker.sln                # 解决方案文件
```

### 依赖包安装
```bash
# 还原所有项目依赖
dotnet restore
```

### 构建项目
```bash
# 构建整个解决方案
dotnet build MqttBroker.sln
```

### 运行服务器
```bash
# 运行 MQTT Broker 服务器
dotnet run --project src/MqttBroker.Host

# 或者使用开发环境配置
dotnet run --project src/MqttBroker.Host --environment Development
```

### 运行测试
```bash
# 运行所有测试
dotnet test

# 运行特定测试项目
dotnet test tests/MqttBroker.Tests.Unit
dotnet test tests/MqttBroker.Tests.Integration
dotnet test tests/MqttBroker.Tests.Performance
```

### 配置说明
- **生产环境配置**: `src/MqttBroker.Host/appsettings.json`
- **开发环境配置**: `src/MqttBroker.Host/appsettings.Development.json`
- **默认端口**: 1883 (MQTT), 8883 (MQTT over TLS)
- **数据库**: SQLite (存储在 `data/` 目录)

## 性能目标
- 支持 10,000+ 并发连接
- 消息延迟 < 10ms (P99)
- 内存使用优化（避免大对象堆分配）
- CPU 使用率 < 80% (正常负载)

## 部署指南
[后续添加 Docker 容器化部署、Linux 服务部署等内容]

## API 文档
[后续添加管理 API 接口文档]

## 贡献指南
[后续添加代码规范、提交规范等内容]
